-- =============================================
-- 为算法结果详情表添加唯一性约束
-- 解决增量分析重复数据问题
-- =============================================

-- 1. contract_risk_details表 - 通用风险详情表
-- 约束逻辑：同一个算法结果中，同一用户的同一检测类型在同一合约上不应重复
ALTER TABLE contract_risk_details 
ADD CONSTRAINT uk_contract_risk_details 
UNIQUE (algorithm_result_id, member_id, detection_type, contract_name);

-- 2. same_account_wash_trading表 - 同账户对敲详情表  
-- 约束逻辑：同一对敲交易中，同一用户在同一合约上的同一组多空持仓不应重复
ALTER TABLE same_account_wash_trading 
ADD CONSTRAINT uk_same_account_wash_trading 
UNIQUE (wash_trading_id, user_id, contract_name, long_position_id, short_position_id);

-- 3. cross_account_wash_trading表 - 跨账户对敲详情表
-- 约束逻辑：同一对敲交易中，同一对用户在同一合约上不应重复
ALTER TABLE cross_account_wash_trading 
ADD CONSTRAINT uk_cross_account_wash_trading 
UNIQUE (wash_trading_id, user_a_id, user_b_id, contract_name);

-- 4. high_frequency_trading_details表 - 高频交易详情表
-- 约束逻辑：同一算法结果中，同一用户在同一合约上不应重复
ALTER TABLE high_frequency_trading_details 
ADD CONSTRAINT uk_high_frequency_trading_details 
UNIQUE (algorithm_result_id, user_id, contract_name);

-- 5. funding_rate_arbitrage_details表 - 资金费率套利详情表
-- 约束逻辑：同一算法结果中，同一用户在同一合约上不应重复
ALTER TABLE funding_rate_arbitrage_details 
ADD CONSTRAINT uk_funding_rate_arbitrage_details 
UNIQUE (algorithm_result_id, user_id, contract_name);

-- =============================================
-- 创建相关索引优化查询性能
-- =============================================

-- contract_risk_details表索引
CREATE INDEX IF NOT EXISTS idx_contract_risk_details_member_detection 
ON contract_risk_details (member_id, detection_type);

CREATE INDEX IF NOT EXISTS idx_contract_risk_details_algorithm_result 
ON contract_risk_details (algorithm_result_id);

-- same_account_wash_trading表索引
CREATE INDEX IF NOT EXISTS idx_same_account_wash_trading_user_contract 
ON same_account_wash_trading (user_id, contract_name);

CREATE INDEX IF NOT EXISTS idx_same_account_wash_trading_wash_id 
ON same_account_wash_trading (wash_trading_id);

-- cross_account_wash_trading表索引
CREATE INDEX IF NOT EXISTS idx_cross_account_wash_trading_users 
ON cross_account_wash_trading (user_a_id, user_b_id);

CREATE INDEX IF NOT EXISTS idx_cross_account_wash_trading_wash_id 
ON cross_account_wash_trading (wash_trading_id);

-- high_frequency_trading_details表索引
CREATE INDEX IF NOT EXISTS idx_high_frequency_trading_details_user_contract 
ON high_frequency_trading_details (user_id, contract_name);

CREATE INDEX IF NOT EXISTS idx_high_frequency_trading_details_algorithm_result 
ON high_frequency_trading_details (algorithm_result_id);

-- funding_rate_arbitrage_details表索引
CREATE INDEX IF NOT EXISTS idx_funding_rate_arbitrage_details_user_contract 
ON funding_rate_arbitrage_details (user_id, contract_name);

CREATE INDEX IF NOT EXISTS idx_funding_rate_arbitrage_details_algorithm_result 
ON funding_rate_arbitrage_details (algorithm_result_id);

-- =============================================
-- 执行完成提示
-- =============================================
-- 执行此脚本后，所有算法结果详情表将具备去重能力
-- 配合已修改的INSERT OR REPLACE语句，可以完全避免重复数据
