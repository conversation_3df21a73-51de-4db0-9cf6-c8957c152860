#!/usr/bin/env python3
"""
应用算法结果详情表去重修复
解决增量分析重复数据问题

执行步骤：
1. 检查现有重复数据
2. 清理重复数据（保留最新的）
3. 添加唯一性约束
4. 验证修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.duckdb_manager import DuckDBManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DeduplicationFixer:
    def __init__(self):
        self.db_manager = DuckDBManager()
        self.tables_config = {
            'contract_risk_details': {
                'unique_fields': ['algorithm_result_id', 'member_id', 'detection_type', 'contract_name'],
                'constraint_name': 'uk_contract_risk_details'
            },
            'same_account_wash_trading': {
                'unique_fields': ['wash_trading_id', 'user_id', 'contract_name', 'long_position_id', 'short_position_id'],
                'constraint_name': 'uk_same_account_wash_trading'
            },
            'cross_account_wash_trading': {
                'unique_fields': ['wash_trading_id', 'user_a_id', 'user_b_id', 'contract_name'],
                'constraint_name': 'uk_cross_account_wash_trading'
            },
            'high_frequency_trading_details': {
                'unique_fields': ['algorithm_result_id', 'user_id', 'contract_name'],
                'constraint_name': 'uk_high_frequency_trading_details'
            },
            'funding_rate_arbitrage_details': {
                'unique_fields': ['algorithm_result_id', 'user_id', 'contract_name'],
                'constraint_name': 'uk_funding_rate_arbitrage_details'
            }
        }
    
    def check_table_exists(self, table_name):
        """检查表是否存在"""
        try:
            sql = """
            SELECT COUNT(*) as table_exists 
            FROM information_schema.tables 
            WHERE table_name = ?
            """
            result = self.db_manager.execute_sql(sql, [table_name])
            return result[0]['table_exists'] > 0
        except Exception as e:
            logger.warning(f"检查表 {table_name} 是否存在时出错: {e}")
            return False
    
    def check_duplicates(self, table_name, unique_fields):
        """检查重复数据"""
        try:
            if not self.check_table_exists(table_name):
                logger.warning(f"表 {table_name} 不存在，跳过重复检查")
                return 0
            
            fields_str = ', '.join(unique_fields)
            sql = f"""
            SELECT {fields_str}, COUNT(*) as duplicate_count
            FROM {table_name}
            GROUP BY {fields_str}
            HAVING COUNT(*) > 1
            """
            
            duplicates = self.db_manager.execute_sql(sql)
            duplicate_groups = len(duplicates)
            total_duplicates = sum(row['duplicate_count'] - 1 for row in duplicates)
            
            if duplicate_groups > 0:
                logger.warning(f"表 {table_name} 发现 {duplicate_groups} 组重复数据，共 {total_duplicates} 条重复记录")
            else:
                logger.info(f"表 {table_name} 无重复数据")
            
            return total_duplicates
            
        except Exception as e:
            logger.error(f"检查表 {table_name} 重复数据失败: {e}")
            return 0
    
    def clean_duplicates(self, table_name, unique_fields):
        """清理重复数据，保留最新的记录"""
        try:
            if not self.check_table_exists(table_name):
                logger.warning(f"表 {table_name} 不存在，跳过清理")
                return 0
            
            fields_str = ', '.join(unique_fields)
            
            # 删除重复数据，保留每组中id最大的记录
            sql = f"""
            DELETE FROM {table_name}
            WHERE id NOT IN (
                SELECT MAX(id)
                FROM {table_name}
                GROUP BY {fields_str}
            )
            """
            
            result = self.db_manager.execute_sql(sql)
            logger.info(f"表 {table_name} 重复数据清理完成")
            return 0
            
        except Exception as e:
            logger.error(f"清理表 {table_name} 重复数据失败: {e}")
            return 0
    
    def add_unique_constraint(self, table_name, unique_fields, constraint_name):
        """添加唯一性约束"""
        try:
            if not self.check_table_exists(table_name):
                logger.warning(f"表 {table_name} 不存在，跳过约束添加")
                return False
            
            fields_str = ', '.join(unique_fields)
            sql = f"""
            ALTER TABLE {table_name} 
            ADD CONSTRAINT {constraint_name} 
            UNIQUE ({fields_str})
            """
            
            self.db_manager.execute_sql(sql)
            logger.info(f"表 {table_name} 唯一性约束 {constraint_name} 添加成功")
            return True
            
        except Exception as e:
            if "already exists" in str(e).lower() or "duplicate" in str(e).lower():
                logger.info(f"表 {table_name} 唯一性约束 {constraint_name} 已存在")
                return True
            else:
                logger.error(f"为表 {table_name} 添加唯一性约束失败: {e}")
                return False
    
    def apply_fix(self):
        """应用完整的去重修复"""
        logger.info("开始应用算法结果详情表去重修复...")
        
        total_duplicates = 0
        successful_tables = 0
        
        for table_name, config in self.tables_config.items():
            logger.info(f"\n处理表: {table_name}")
            
            # 1. 检查重复数据
            duplicates = self.check_duplicates(table_name, config['unique_fields'])
            total_duplicates += duplicates
            
            # 2. 清理重复数据
            if duplicates > 0:
                self.clean_duplicates(table_name, config['unique_fields'])
            
            # 3. 添加唯一性约束
            if self.add_unique_constraint(table_name, config['unique_fields'], config['constraint_name']):
                successful_tables += 1
        
        logger.info(f"\n去重修复完成:")
        logger.info(f"- 处理表数量: {len(self.tables_config)}")
        logger.info(f"- 成功添加约束: {successful_tables}")
        logger.info(f"- 清理重复记录: {total_duplicates}")
        logger.info(f"- INSERT OR REPLACE 语句已修改完成")
        
        return successful_tables == len(self.tables_config)

def main():
    """主函数"""
    try:
        fixer = DeduplicationFixer()
        success = fixer.apply_fix()
        
        if success:
            logger.info("\n✅ 去重修复应用成功！")
            logger.info("现在增量分析将不会产生重复数据。")
        else:
            logger.error("\n❌ 去重修复应用失败，请检查日志。")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"应用去重修复时发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
